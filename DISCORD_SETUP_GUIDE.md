# Discord Bot Notification Setup Guide

## 🎯 Quick Setup for Channel ID: 1385057459814797383

Your SmaTrendFollower system is configured to send trading notifications to Discord channel ID `1385057459814797383` using a Discord bot. The bot token is already configured.

## 📋 Current Configuration

### ✅ **Bot Token Configured**
- **Bot Token**: `MTM4NTA1OTI3MDMzNjMxNTQ1NA.GtTKUd.fuCC2ZI-H-tTLZl41YF3gZj-w3gPbv_Xep8NoE`
- **Channel ID**: `1385057459814797383`
- **API Version**: Discord API v10

### ✅ **Environment Variables Set**
Your `.env` file is configured with:
```bash
DISCORD_BOT_TOKEN=MTM4NTA1OTI3MDMzNjMxNTQ1NA.GtTKUd.fuCC2ZI-H-tTLZl41YF3gZj-w3gPbv_Xep8NoE
DISCORD_CHANNEL_ID=1385057459814797383
```

### 🔧 **Bot Permissions Required**
Ensure the bot has these permissions in the target channel:
- **Send Messages** - To post notifications
- **Use External Emojis** - For rich formatting
- **Embed Links** - For rich embed messages
- **Read Message History** - For context (optional)

## 🔔 Notification Types

Your SmaTrendFollower will send these types of notifications:

### 📈 **Trade Execution Alerts**
- **BUY** orders: Green color with 🟢 emoji
- **SELL** orders: Red color with 🔴 emoji  
- **STOP** orders: Orange color with 🛑 emoji
- Includes symbol, quantity, price, and P&L

### 💼 **Portfolio Snapshots**
- Total equity and position count
- Daily P&L with percentage
- Total P&L tracking
- Color-coded: Green for profits, Red for losses

### ⚠️ **VIX Spike Alerts**
- Triggered when VIX exceeds threshold (default: 25.0)
- Orange color with warning emojis
- Includes current VIX level and recommended action

### 🎯 **Options Strategy Notifications**
- **Protective Put**: 🛡️ Portfolio insurance alerts
- **Covered Call**: 📞 Income generation opportunities
- **Delta Efficient**: ⚡ Leveraged exposure strategies
- Purple color with strategy details

## 🧪 Testing Your Setup

### ✅ **Ready to Test**
The bot is already configured and ready to send notifications!

### Test Discord Notifications
Run the enhanced features example to test notifications:
```bash
dotnet run --project SmaTrendFollower.Console -- --example enhanced-features
```

### Manual Test
You can also test by running a single trading cycle:
```bash
dotnet run --project SmaTrendFollower.Console
```

### Verify Bot Status
Check that the bot is online and has access to the channel:
1. Look for the bot in the channel member list
2. Ensure it has the required permissions
3. Test with a simple message first

## 🛡️ Security Best Practices

### ✅ **Do's**
- Keep your webhook URL private and secure
- Use environment variables (never hardcode in source)
- Regularly rotate webhook tokens if compromised
- Monitor webhook usage in Discord audit logs

### ❌ **Don'ts**
- Don't commit webhook URLs to version control
- Don't share webhook URLs in public channels
- Don't use the same webhook for multiple applications
- Don't ignore failed webhook delivery logs

## 🔧 Troubleshooting

### Common Issues

#### **Webhook Not Working**
1. **Check URL Format**: Ensure it starts with `https://discord.com/api/webhooks/`
2. **Verify Permissions**: Ensure bot has webhook permissions in the channel
3. **Test URL**: Try sending a test message using curl or Postman
4. **Check Logs**: Look for Discord-related errors in application logs

#### **Messages Not Appearing**
1. **Channel Permissions**: Verify the webhook can post to the channel
2. **Rate Limiting**: Discord has rate limits (30 requests per minute per webhook)
3. **Message Format**: Ensure JSON payload is properly formatted
4. **Webhook Status**: Check if webhook was deleted or disabled

#### **Error Messages**
- **401 Unauthorized**: Invalid webhook URL or token
- **404 Not Found**: Webhook was deleted or URL is incorrect
- **429 Too Many Requests**: Rate limit exceeded, implement backoff
- **400 Bad Request**: Invalid message format or content

### Debug Commands
```bash
# Check environment variables
echo $DISCORD_WEBHOOK_URL

# Test webhook with curl
curl -X POST "$DISCORD_WEBHOOK_URL" \
  -H "Content-Type: application/json" \
  -d '{"content": "Test message from SmaTrendFollower"}'

# View application logs
tail -f logs/sma-trend-follower-*.log | grep -i discord
```

## 📊 Webhook Analytics

### Monitor Usage
- Discord provides webhook usage statistics
- Track delivery success/failure rates
- Monitor rate limit usage
- Review message engagement

### Performance Tips
- Batch multiple notifications when possible
- Use embeds for rich formatting
- Implement exponential backoff for retries
- Cache webhook responses to avoid duplicates

## 🎉 Setup Complete!

Once configured, your SmaTrendFollower will automatically send:
- Real-time trade execution alerts
- Daily portfolio snapshots
- VIX spike warnings
- Options strategy recommendations

All notifications will appear in Discord channel `1385057459814797383` with rich formatting, colors, and emojis for easy monitoring of your trading system.

## 📞 Support

If you encounter issues:
1. Check the application logs for Discord-related errors
2. Verify webhook URL format and permissions
3. Test webhook manually with curl
4. Review Discord's webhook documentation
5. Check rate limiting and retry logic

Your trading notifications are now ready to keep you informed of all SmaTrendFollower activity! 🚀
