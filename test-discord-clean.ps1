#!/usr/bin/env pwsh
<#
.SYNOPSIS
    Clean Discord bot test without Unicode characters
#>

$ErrorActionPreference = "Continue"

Write-Host "Discord Bot Configuration Test" -ForegroundColor Green
Write-Host "==============================" -ForegroundColor Green

# Load environment variables from .env
if (Test-Path ".env") {
    Write-Host "Loading .env file..." -ForegroundColor Cyan
    
    $envContent = Get-Content ".env"
    foreach ($line in $envContent) {
        if ($line -and $line.Trim() -and -not $line.StartsWith("#")) {
            $parts = $line.Split("=", 2)
            if ($parts.Length -eq 2) {
                $name = $parts[0].Trim()
                $value = $parts[1].Trim()
                [Environment]::SetEnvironmentVariable($name, $value, "Process")
                Write-Host "  Set $name" -ForegroundColor Gray
            }
        }
    }
} else {
    Write-Host "ERROR: .env file not found" -ForegroundColor Red
    exit 1
}

# Get configuration
$botToken = $env:DISCORD_BOT_TOKEN
$channelId = $env:DISCORD_CHANNEL_ID

Write-Host "`nConfiguration:" -ForegroundColor Yellow
Write-Host "Bot Token: $($botToken.Substring(0, 20))..." -ForegroundColor Green
Write-Host "Channel ID: $channelId" -ForegroundColor Green

# Test 1: Validate bot token
Write-Host "`nTesting bot token..." -ForegroundColor Cyan

$headers = @{
    "Authorization" = "Bot $botToken"
    "User-Agent" = "SmaTrendFollower/1.0"
}

try {
    $botInfo = Invoke-RestMethod -Uri "https://discord.com/api/v10/users/@me" -Headers $headers
    Write-Host "SUCCESS: Bot token is valid!" -ForegroundColor Green
    Write-Host "  Bot Name: $($botInfo.username)" -ForegroundColor Gray
    Write-Host "  Bot ID: $($botInfo.id)" -ForegroundColor Gray
} catch {
    Write-Host "ERROR: Bot token validation failed" -ForegroundColor Red
    Write-Host "  $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Test 2: Check channel access
Write-Host "`nTesting channel access..." -ForegroundColor Cyan

try {
    $channelInfo = Invoke-RestMethod -Uri "https://discord.com/api/v10/channels/$channelId" -Headers $headers
    Write-Host "SUCCESS: Channel is accessible!" -ForegroundColor Green
    Write-Host "  Channel Name: $($channelInfo.name)" -ForegroundColor Gray
    Write-Host "  Channel Type: $($channelInfo.type)" -ForegroundColor Gray
} catch {
    Write-Host "ERROR: Channel access failed" -ForegroundColor Red
    Write-Host "  $($_.Exception.Message)" -ForegroundColor Red
    
    $statusCode = 0
    if ($_.Exception.Response) {
        $statusCode = [int]$_.Exception.Response.StatusCode
    }
    
    switch ($statusCode) {
        403 { Write-Host "  HINT: Bot lacks permission to view this channel" -ForegroundColor Yellow }
        404 { Write-Host "  HINT: Channel not found or bot not in server" -ForegroundColor Yellow }
        default { Write-Host "  HINT: Check bot permissions and server membership" -ForegroundColor Yellow }
    }
    exit 1
}

# Test 3: Send test message
Write-Host "`nSending test message..." -ForegroundColor Cyan

$messageHeaders = $headers.Clone()
$messageHeaders["Content-Type"] = "application/json"

$timestamp = Get-Date -Format 'yyyy-MM-dd HH:mm:ss UTC'
$testMessage = @{
    content = "SmaTrendFollower Bot Test - Configuration validated successfully! Timestamp: $timestamp"
} | ConvertTo-Json

try {
    $response = Invoke-RestMethod -Uri "https://discord.com/api/v10/channels/$channelId/messages" -Method Post -Headers $messageHeaders -Body $testMessage
    Write-Host "SUCCESS: Test message sent!" -ForegroundColor Green
    Write-Host "  Message ID: $($response.id)" -ForegroundColor Gray
    Write-Host "  Timestamp: $($response.timestamp)" -ForegroundColor Gray
} catch {
    Write-Host "ERROR: Failed to send message" -ForegroundColor Red
    Write-Host "  $($_.Exception.Message)" -ForegroundColor Red
    
    $statusCode = 0
    if ($_.Exception.Response) {
        $statusCode = [int]$_.Exception.Response.StatusCode
    }
    
    switch ($statusCode) {
        400 { Write-Host "  HINT: Bad request format" -ForegroundColor Yellow }
        403 { Write-Host "  HINT: Bot lacks Send Messages permission" -ForegroundColor Yellow }
        404 { Write-Host "  HINT: Channel not found" -ForegroundColor Yellow }
        429 { Write-Host "  HINT: Rate limited" -ForegroundColor Yellow }
        default { Write-Host "  HINT: Unknown error (Status: $statusCode)" -ForegroundColor Yellow }
    }
    exit 1
}

Write-Host "`nAll tests passed!" -ForegroundColor Green
Write-Host "Your Discord bot is properly configured and ready to send notifications." -ForegroundColor Cyan
Write-Host "`nNext steps:" -ForegroundColor Yellow
Write-Host "1. Run your SmaTrendFollower application" -ForegroundColor White
Write-Host "2. Monitor Discord channel $channelId for trading notifications" -ForegroundColor White
