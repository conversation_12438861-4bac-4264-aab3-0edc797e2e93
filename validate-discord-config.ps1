#!/usr/bin/env pwsh
<#
.SYNOPSIS
    Validate Discord configuration and test bot connectivity
#>

$ErrorActionPreference = "Continue"

Write-Host "🔍 Discord Configuration Validation" -ForegroundColor Green
Write-Host "====================================" -ForegroundColor Green

# Load environment variables
if (Test-Path ".env") {
    Write-Host "✅ Loading .env file..." -ForegroundColor Cyan
    Get-Content ".env" | ForEach-Object {
        if ($_ -match "^([^#][^=]+)=(.*)$") {
            $name = $matches[1].Trim()
            $value = $matches[2].Trim()
            [Environment]::SetEnvironmentVariable($name, $value, "Process")
        }
    }
} else {
    Write-Host "❌ .env file not found" -ForegroundColor Red
    exit 1
}

$botToken = $env:DISCORD_BOT_TOKEN
$channelId = $env:DISCORD_CHANNEL_ID

# Validate configuration
Write-Host "`n🔧 Configuration Validation:" -ForegroundColor Yellow

if ($botToken) {
    Write-Host "✅ Bot token found" -ForegroundColor Green
    Write-Host "   Length: $($botToken.Length) characters" -ForegroundColor Gray
    Write-Host "   Prefix: $($botToken.Substring(0, [Math]::Min(20, $botToken.Length)))..." -ForegroundColor Gray
    
    # Validate bot token format (should be base64-like)
    if ($botToken -match "^[A-Za-z0-9+/=._-]+$") {
        Write-Host "✅ Bot token format appears valid" -ForegroundColor Green
    } else {
        Write-Host "⚠️  Bot token format may be invalid" -ForegroundColor Yellow
    }
} else {
    Write-Host "❌ Bot token not found" -ForegroundColor Red
}

if ($channelId) {
    Write-Host "✅ Channel ID found: $channelId" -ForegroundColor Green
    
    # Validate channel ID format (should be numeric)
    if ($channelId -match "^\d+$") {
        Write-Host "✅ Channel ID format is valid" -ForegroundColor Green
    } else {
        Write-Host "❌ Channel ID format is invalid (should be numeric)" -ForegroundColor Red
    }
} else {
    Write-Host "❌ Channel ID not found" -ForegroundColor Red
}

if (-not $botToken -or -not $channelId) {
    Write-Host "`n❌ Configuration incomplete. Cannot proceed with tests." -ForegroundColor Red
    exit 1
}

# Test 1: Check if we can reach Discord API
Write-Host "`n🌐 Testing Discord API connectivity..." -ForegroundColor Yellow

try {
    $testResponse = Invoke-WebRequest -Uri "https://discord.com/api/v10/gateway" -UseBasicParsing -TimeoutSec 10
    Write-Host "✅ Discord API is reachable" -ForegroundColor Green
    Write-Host "   Status: $($testResponse.StatusCode)" -ForegroundColor Gray
} catch {
    Write-Host "❌ Cannot reach Discord API: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 2: Validate bot token by getting bot user info
Write-Host "`n🤖 Testing bot token validity..." -ForegroundColor Yellow

$headers = @{
    "Authorization" = "Bot $botToken"
    "User-Agent" = "SmaTrendFollower/1.0"
}

try {
    $botInfo = Invoke-RestMethod -Uri "https://discord.com/api/v10/users/@me" -Headers $headers -TimeoutSec 10
    Write-Host "✅ Bot token is valid!" -ForegroundColor Green
    Write-Host "   Bot Name: $($botInfo.username)" -ForegroundColor Gray
    Write-Host "   Bot ID: $($botInfo.id)" -ForegroundColor Gray
    Write-Host "   Bot Discriminator: $($botInfo.discriminator)" -ForegroundColor Gray
} catch {
    Write-Host "❌ Bot token validation failed: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response.StatusCode -eq 401) {
        Write-Host "   HINT: Bot token is invalid or expired" -ForegroundColor Yellow
    }
}

# Test 3: Check channel accessibility
Write-Host "`n📢 Testing channel access..." -ForegroundColor Yellow

try {
    $channelInfo = Invoke-RestMethod -Uri "https://discord.com/api/v10/channels/$channelId" -Headers $headers -TimeoutSec 10
    Write-Host "✅ Channel is accessible!" -ForegroundColor Green
    Write-Host "   Channel Name: $($channelInfo.name)" -ForegroundColor Gray
    Write-Host "   Channel Type: $($channelInfo.type)" -ForegroundColor Gray
    Write-Host "   Guild ID: $($channelInfo.guild_id)" -ForegroundColor Gray
} catch {
    Write-Host "❌ Channel access failed: $($_.Exception.Message)" -ForegroundColor Red
    $statusCode = $_.Exception.Response.StatusCode.value__
    switch ($statusCode) {
        403 { Write-Host "   HINT: Bot lacks permission to view this channel" -ForegroundColor Yellow }
        404 { Write-Host "   HINT: Channel not found or bot not in server" -ForegroundColor Yellow }
        default { Write-Host "   HINT: Unknown error (Status: $statusCode)" -ForegroundColor Yellow }
    }
}

# Test 4: Try sending a test message
Write-Host "`n💬 Testing message sending..." -ForegroundColor Yellow

$messagePayload = @{
    content = "🧪 **SmaTrendFollower Bot Test**`nConfiguration validation successful!`nTimestamp: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss UTC')"
} | ConvertTo-Json -Compress

$messageHeaders = $headers.Clone()
$messageHeaders["Content-Type"] = "application/json"

try {
    $messageResponse = Invoke-RestMethod -Uri "https://discord.com/api/v10/channels/$channelId/messages" -Method Post -Headers $messageHeaders -Body $messagePayload -TimeoutSec 10
    Write-Host "✅ Test message sent successfully!" -ForegroundColor Green
    Write-Host "   Message ID: $($messageResponse.id)" -ForegroundColor Gray
    Write-Host "   Timestamp: $($messageResponse.timestamp)" -ForegroundColor Gray
} catch {
    Write-Host "❌ Message sending failed: $($_.Exception.Message)" -ForegroundColor Red
    
    if ($_.ErrorDetails) {
        Write-Host "   Error Details: $($_.ErrorDetails.Message)" -ForegroundColor Red
    }
    
    $statusCode = $_.Exception.Response.StatusCode.value__
    switch ($statusCode) {
        400 { Write-Host "   HINT: Bad request - check message format" -ForegroundColor Yellow }
        403 { Write-Host "   HINT: Bot lacks 'Send Messages' permission in this channel" -ForegroundColor Yellow }
        404 { Write-Host "   HINT: Channel not found" -ForegroundColor Yellow }
        429 { Write-Host "   HINT: Rate limited - try again later" -ForegroundColor Yellow }
        default { Write-Host "   HINT: Unknown error (Status: $statusCode)" -ForegroundColor Yellow }
    }
}

Write-Host "`n📋 Validation Summary:" -ForegroundColor Magenta
Write-Host "======================" -ForegroundColor Magenta
Write-Host "Bot Token: $(if ($botToken) { '✅ Configured' } else { '❌ Missing' })" -ForegroundColor $(if ($botToken) { 'Green' } else { 'Red' })
Write-Host "Channel ID: $(if ($channelId) { '✅ Configured' } else { '❌ Missing' })" -ForegroundColor $(if ($channelId) { 'Green' } else { 'Red' })
Write-Host "Discord API: ✅ Reachable" -ForegroundColor Green

Write-Host "`n🎯 Next Steps:" -ForegroundColor Cyan
Write-Host "1. Ensure the bot is added to your Discord server" -ForegroundColor White
Write-Host "2. Verify the bot has Send Messages permission in channel $channelId" -ForegroundColor White
Write-Host "3. Test with: dotnet run --project SmaTrendFollower.Console" -ForegroundColor White
