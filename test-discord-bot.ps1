#!/usr/bin/env pwsh
<#
.SYNOPSIS
    Test script for Discord bot notifications
.DESCRIPTION
    This script tests the Discord bot configuration by sending a test message
    to the configured channel using the bot token.
.EXAMPLE
    .\test-discord-bot.ps1
#>

$ErrorActionPreference = "Stop"

Write-Host "Testing Discord Bot Configuration..." -ForegroundColor Green

# Load environment variables from .env file
if (Test-Path ".env") {
    Write-Host "Loading environment variables from .env file..." -ForegroundColor Cyan
    
    Get-Content ".env" | ForEach-Object {
        if ($_ -match "^([^#][^=]+)=(.*)$") {
            $name = $matches[1].Trim()
            $value = $matches[2].Trim()
            [Environment]::SetEnvironmentVariable($name, $value, "Process")
            Write-Host "  Set $name" -ForegroundColor Gray
        }
    }
} else {
    Write-Host "ERROR: .env file not found" -ForegroundColor Red
    exit 1
}

# Get configuration
$botToken = $env:DISCORD_BOT_TOKEN
$channelId = $env:DISCORD_CHANNEL_ID

if (-not $botToken) {
    Write-Host "ERROR: DISCORD_BOT_TOKEN not set in environment" -ForegroundColor Red
    exit 1
}

if (-not $channelId) {
    Write-Host "ERROR: DISCORD_CHANNEL_ID not set in environment" -ForegroundColor Red
    exit 1
}

Write-Host "Bot Token: $($botToken.Substring(0, 20))..." -ForegroundColor Green
Write-Host "Channel ID: $channelId" -ForegroundColor Green

# Create test message
$embed = @{
    title = "🧪 SmaTrendFollower Bot Test"
    description = "This is a test message to verify Discord bot configuration is working correctly."
    color = 0x00FF00  # Green
    timestamp = (Get-Date).ToUniversalTime().ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
    footer = @{
        text = "SmaTrendFollower Bot"
        icon_url = "https://cdn.discordapp.com/embed/avatars/0.png"
    }
    fields = @(
        @{
            name = "✅ Configuration Status"
            value = "Bot token and channel ID are properly configured"
            inline = $false
        },
        @{
            name = "🎯 Target Channel"
            value = "Channel ID: $channelId"
            inline = $true
        },
        @{
            name = "🤖 Bot Status"
            value = "Ready to send trading notifications"
            inline = $true
        }
    )
}

$payload = @{
    embeds = @($embed)
} | ConvertTo-Json -Depth 10

# Send test message
try {
    Write-Host "Sending test message to Discord..." -ForegroundColor Cyan
    
    $headers = @{
        "Authorization" = "Bot $botToken"
        "Content-Type" = "application/json"
    }
    
    $apiUrl = "https://discord.com/api/v10/channels/$channelId/messages"
    
    $response = Invoke-RestMethod -Uri $apiUrl -Method Post -Headers $headers -Body $payload -Verbose
    
    Write-Host "SUCCESS: Test message sent successfully!" -ForegroundColor Green
    Write-Host "Message ID: $($response.id)" -ForegroundColor Gray
    Write-Host "Timestamp: $($response.timestamp)" -ForegroundColor Gray
    
} catch {
    Write-Host "ERROR: Failed to send test message" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    
    if ($_.Exception.Response) {
        $statusCode = $_.Exception.Response.StatusCode
        Write-Host "HTTP Status: $statusCode" -ForegroundColor Red
        
        try {
            $errorContent = $_.Exception.Response.GetResponseStream()
            $reader = New-Object System.IO.StreamReader($errorContent)
            $errorBody = $reader.ReadToEnd()
            Write-Host "Response Body: $errorBody" -ForegroundColor Red
        } catch {
            Write-Host "Could not read error response body" -ForegroundColor Red
        }
    }
    
    exit 1
}

Write-Host "`nDiscord bot test completed successfully!" -ForegroundColor Green
Write-Host "Your SmaTrendFollower is ready to send trading notifications to Discord." -ForegroundColor Cyan
