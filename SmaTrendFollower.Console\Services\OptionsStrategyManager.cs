using Alpaca.Markets;
using Microsoft.Extensions.Logging;

namespace SmaTrendFollower.Services;

/// <summary>
/// Manages options overlay strategies for enhanced returns and risk management
/// </summary>
public sealed class OptionsStrategyManager : IOptionsStrategyManager
{
    private readonly IMarketDataService _marketDataService;
    private readonly IAlpacaClientFactory _alpacaFactory;
    private readonly IVolatilityManager _volatilityManager;
    private readonly ILogger<OptionsStrategyManager> _logger;

    public OptionsStrategyManager(
        IMarketDataService marketDataService,
        IAlpacaClientFactory alpacaFactory,
        IVolatilityManager volatilityManager,
        ILogger<OptionsStrategyManager> logger)
    {
        _marketDataService = marketDataService;
        _alpacaFactory = alpacaFactory;
        _volatilityManager = volatilityManager;
        _logger = logger;
    }

    public async Task<ProtectivePutResult> EvaluateProtectivePutAsync(string symbol, decimal portfolioValue, decimal currentPrice)
    {
        try
        {
            // Check if protective put is warranted
            var vixRegime = await _volatilityManager.GetCurrentRegimeAsync();
            var account = await _marketDataService.GetAccountAsync();
            var portfolioPnl = account.Equity - account.LastEquity;
            var portfolioPnlPercent = portfolioPnl / account.LastEquity ?? 0m;

            // Trigger conditions for protective puts
            bool shouldTrigger = portfolioPnlPercent <= -0.03m || // Portfolio down 3%
                               vixRegime.CurrentVix > vixRegime.VixSma30 || // VIX above 200-SMA
                               symbol == "SPY"; // Always consider for SPY

            if (!shouldTrigger)
            {
                return new ProtectivePutResult(false, null, 0, DateTime.MinValue, 0, 0, 
                    "No trigger conditions met");
            }

            // Get protective put options (1-month ATM puts)
            var putOptions = await _marketDataService.GetProtectivePutOptionsAsync(symbol, 30);
            var atmPut = putOptions
                .Where(o => o.OptionType.ToLower() == "put")
                .Where(o => Math.Abs(o.Strike - currentPrice) / currentPrice < 0.02m) // Within 2% of ATM
                .OrderBy(o => Math.Abs(o.Strike - currentPrice))
                .FirstOrDefault();

            if (atmPut.Symbol == null)
            {
                return new ProtectivePutResult(false, null, 0, DateTime.MinValue, 0, 0,
                    "No suitable ATM put options found");
            }

            // Calculate protection level and cost
            var notionalValue = portfolioValue / 100_000m; // Number of $100k blocks
            var contractsNeeded = Math.Ceiling(notionalValue);
            var totalPremium = contractsNeeded * (atmPut.LastPrice ?? atmPut.Ask ?? 0m) * 100m; // Options are per 100 shares
            var protectionLevel = (currentPrice - atmPut.Strike) / currentPrice;

            // Cost-benefit analysis
            var maxAcceptableCost = portfolioValue * 0.02m; // Max 2% of portfolio for protection
            bool costEffective = totalPremium <= maxAcceptableCost;

            if (!costEffective)
            {
                return new ProtectivePutResult(false, atmPut.Symbol, atmPut.Strike, atmPut.ExpirationDate,
                    totalPremium, protectionLevel, $"Cost too high: ${totalPremium:N0} > ${maxAcceptableCost:N0}");
            }

            return new ProtectivePutResult(true, atmPut.Symbol, atmPut.Strike, atmPut.ExpirationDate,
                totalPremium, protectionLevel, $"Protective put recommended: {protectionLevel:P1} protection for ${totalPremium:N0}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error evaluating protective put for {Symbol}", symbol);
            return new ProtectivePutResult(false, null, 0, DateTime.MinValue, 0, 0, $"Error: {ex.Message}");
        }
    }

    public async Task<CoveredCallResult> EvaluateCoveredCallAsync(string symbol, decimal sharesOwned, decimal currentPrice)
    {
        try
        {
            if (sharesOwned <= 0)
            {
                return new CoveredCallResult(false, null, 0, DateTime.MinValue, 0, 0, 0,
                    "No shares owned for covered call");
            }

            // Get covered call options (weekly 0.15 delta calls)
            var callOptions = await _marketDataService.GetCoveredCallOptionsAsync(symbol, currentPrice, 7);
            var targetDeltaCall = callOptions
                .Where(o => o.OptionType.ToLower() == "call")
                .Where(o => o.Strike > currentPrice) // OTM calls only
                .Where(o => o.Delta.HasValue && o.Delta >= 0.10m && o.Delta <= 0.20m) // Target ~0.15 delta
                .OrderBy(o => Math.Abs(o.Delta!.Value - 0.15m))
                .FirstOrDefault();

            if (targetDeltaCall.Symbol == null)
            {
                return new CoveredCallResult(false, null, 0, DateTime.MinValue, 0, 0, 0,
                    "No suitable 0.15 delta call options found");
            }

            // Calculate covered call metrics
            var contractsToSell = Math.Floor(sharesOwned / 100m); // Each contract covers 100 shares
            if (contractsToSell <= 0)
            {
                return new CoveredCallResult(false, targetDeltaCall.Symbol, targetDeltaCall.Strike, 
                    targetDeltaCall.ExpirationDate, 0, 0, 0, "Insufficient shares for covered call (need 100+)");
            }

            var premiumPerContract = targetDeltaCall.Bid ?? targetDeltaCall.LastPrice ?? 0m;
            var totalPremium = contractsToSell * premiumPerContract * 100m; // Options are per 100 shares
            var daysToExpiration = (targetDeltaCall.ExpirationDate - DateTime.UtcNow).Days;
            var annualizedYield = (totalPremium / (sharesOwned * currentPrice)) * (365m / Math.Max(1, daysToExpiration));
            
            // Assignment risk analysis
            var assignmentRisk = targetDeltaCall.Delta ?? 0m; // Delta approximates assignment probability
            var upside = (targetDeltaCall.Strike - currentPrice) / currentPrice;

            // Decision criteria
            bool shouldExecute = annualizedYield > 0.15m && // Target 15%+ annualized yield
                               assignmentRisk < 0.25m && // Low assignment risk
                               upside > 0.02m; // At least 2% upside to strike

            var reason = shouldExecute 
                ? $"Attractive covered call: {annualizedYield:P1} annualized yield, {assignmentRisk:P1} assignment risk"
                : $"Unattractive: {annualizedYield:P1} yield, {assignmentRisk:P1} assignment risk";

            return new CoveredCallResult(shouldExecute, targetDeltaCall.Symbol, targetDeltaCall.Strike,
                targetDeltaCall.ExpirationDate, totalPremium, annualizedYield, assignmentRisk, reason);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error evaluating covered call for {Symbol}", symbol);
            return new CoveredCallResult(false, null, 0, DateTime.MinValue, 0, 0, 0, $"Error: {ex.Message}");
        }
    }

    public async Task<DeltaEfficientResult> EvaluateDeltaEfficientExposureAsync(string symbol, decimal targetExposure, decimal currentPrice)
    {
        try
        {
            // Get deep ITM calls (0.85 delta) and OTM calls for financing
            var callOptions = await _marketDataService.GetOptionsDataAsync(symbol, DateTime.UtcNow.AddDays(60));
            
            var deepItmCall = callOptions
                .Where(o => o.OptionType.ToLower() == "call")
                .Where(o => o.Strike < currentPrice * 0.85m) // Deep ITM
                .Where(o => o.Delta.HasValue && o.Delta >= 0.80m)
                .OrderByDescending(o => o.Delta)
                .FirstOrDefault();

            var otmCall = callOptions
                .Where(o => o.OptionType.ToLower() == "call")
                .Where(o => o.Strike > currentPrice * 1.10m) // OTM for financing
                .Where(o => o.Delta.HasValue && o.Delta <= 0.30m)
                .OrderBy(o => o.Delta)
                .FirstOrDefault();

            if (deepItmCall.Symbol == null || otmCall.Symbol == null)
            {
                return new DeltaEfficientResult(false, null, null, 0, 0, DateTime.MinValue, 0, 0, 0,
                    "Insufficient options chain for delta-efficient strategy");
            }

            // Calculate strategy metrics
            var longCallCost = deepItmCall.Ask ?? deepItmCall.LastPrice ?? 0m;
            var shortCallCredit = otmCall.Bid ?? otmCall.LastPrice ?? 0m;
            var netPremium = (longCallCost - shortCallCredit) * 100m; // Per contract
            
            var effectiveDelta = (deepItmCall.Delta ?? 0.85m) - (otmCall.Delta ?? 0.15m);
            var sharesEquivalent = effectiveDelta * 100m; // Shares equivalent per contract
            var capitalEfficiency = sharesEquivalent * currentPrice / netPremium;

            // Decision criteria
            bool shouldExecute = capitalEfficiency > 2.5m && // At least 2.5x capital efficiency
                               effectiveDelta > 0.60m && // Meaningful equity exposure
                               netPremium < targetExposure * 0.4m; // Use less than 40% of target capital

            var reason = shouldExecute
                ? $"Efficient delta exposure: {capitalEfficiency:F1}x efficiency, {effectiveDelta:P1} delta"
                : $"Inefficient: {capitalEfficiency:F1}x efficiency, {effectiveDelta:P1} delta";

            return new DeltaEfficientResult(shouldExecute, deepItmCall.Symbol, otmCall.Symbol,
                deepItmCall.Strike, otmCall.Strike, deepItmCall.ExpirationDate, netPremium,
                effectiveDelta, capitalEfficiency, reason);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error evaluating delta-efficient exposure for {Symbol}", symbol);
            return new DeltaEfficientResult(false, null, null, 0, 0, DateTime.MinValue, 0, 0, 0, $"Error: {ex.Message}");
        }
    }

    public async Task ManageExistingOptionsAsync()
    {
        try
        {
            var positions = await _marketDataService.GetPositionsAsync();
            var optionsPositions = positions.Where(p => p.Symbol.Contains("C") || p.Symbol.Contains("P")).ToList();

            foreach (var position in optionsPositions)
            {
                await ManageIndividualOptionPosition(position);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error managing existing options positions");
        }
    }

    public async Task ManageExpirationRiskAsync()
    {
        try
        {
            var positions = await _marketDataService.GetPositionsAsync();
            var optionsPositions = positions.Where(p => p.Symbol.Contains("C") || p.Symbol.Contains("P")).ToList();

            var expiringToday = optionsPositions.Where(p => IsExpiringToday(p.Symbol)).ToList();
            var expiringThisWeek = optionsPositions.Where(p => IsExpiringThisWeek(p.Symbol)).ToList();

            foreach (var position in expiringToday)
            {
                await HandleExpiringPosition(position, true);
            }

            foreach (var position in expiringThisWeek)
            {
                await HandleExpiringPosition(position, false);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error managing expiration risk");
        }
    }

    private async Task ManageIndividualOptionPosition(IPosition position)
    {
        // Implementation for managing individual options positions
        // This would include profit-taking, loss-cutting, and rolling strategies
        _logger.LogDebug("Managing options position: {Symbol}, Quantity: {Quantity}, P&L: {PnL:C}",
            position.Symbol, position.Quantity, position.UnrealizedProfitLoss);
    }

    private async Task HandleExpiringPosition(IPosition position, bool isExpiringToday)
    {
        // Implementation for handling expiring options positions
        _logger.LogWarning("Options position expiring {When}: {Symbol}, Quantity: {Quantity}",
            isExpiringToday ? "today" : "this week", position.Symbol, position.Quantity);
    }

    private static bool IsExpiringToday(string optionSymbol)
    {
        // Parse option symbol to determine expiration date
        // This is a simplified implementation
        return false;
    }

    private static bool IsExpiringThisWeek(string optionSymbol)
    {
        // Parse option symbol to determine expiration date
        // This is a simplified implementation
        return false;
    }
}
