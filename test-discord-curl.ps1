#!/usr/bin/env pwsh
<#
.SYNOPSIS
    Test Discord bot using curl for better error reporting
#>

# Load environment variables
if (Test-Path ".env") {
    Get-Content ".env" | ForEach-Object {
        if ($_ -match "^([^#][^=]+)=(.*)$") {
            $name = $matches[1].Trim()
            $value = $matches[2].Trim()
            [Environment]::SetEnvironmentVariable($name, $value, "Process")
        }
    }
}

$botToken = $env:DISCORD_BOT_TOKEN
$channelId = $env:DISCORD_CHANNEL_ID

Write-Host "Testing Discord Bot with curl..." -ForegroundColor Green
Write-Host "Channel ID: $channelId" -ForegroundColor Cyan

# Test with curl for better error reporting
$curlCommand = @"
curl -X POST "https://discord.com/api/v10/channels/$channelId/messages" \
  -H "Authorization: Bot $botToken" \
  -H "Content-Type: application/json" \
  -H "User-Agent: SmaTrendFollower/1.0" \
  -d '{\"content\": \"🧪 Test from SmaTrendFollower Bot via curl\"}' \
  -v
"@

Write-Host "Executing curl command..." -ForegroundColor Cyan
Write-Host $curlCommand -ForegroundColor Gray

try {
    $result = Invoke-Expression $curlCommand
    Write-Host "Curl result: $result" -ForegroundColor Green
} catch {
    Write-Host "Curl failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Also try with Invoke-RestMethod with more detailed error handling
Write-Host "`nTrying with Invoke-RestMethod..." -ForegroundColor Cyan

$headers = @{
    "Authorization" = "Bot $botToken"
    "Content-Type" = "application/json"
    "User-Agent" = "SmaTrendFollower/1.0"
}

$body = @{
    content = "🧪 Test from SmaTrendFollower Bot via PowerShell"
} | ConvertTo-Json -Compress

$uri = "https://discord.com/api/v10/channels/$channelId/messages"

Write-Host "URI: $uri" -ForegroundColor Gray
Write-Host "Headers: $($headers | ConvertTo-Json)" -ForegroundColor Gray
Write-Host "Body: $body" -ForegroundColor Gray

try {
    $response = Invoke-RestMethod -Uri $uri -Method Post -Headers $headers -Body $body -ErrorAction Stop
    Write-Host "SUCCESS!" -ForegroundColor Green
    Write-Host "Response: $($response | ConvertTo-Json)" -ForegroundColor Gray
} catch {
    Write-Host "ERROR: $($_.Exception.Message)" -ForegroundColor Red
    
    if ($_.ErrorDetails) {
        Write-Host "Error Details: $($_.ErrorDetails.Message)" -ForegroundColor Red
    }
    
    if ($_.Exception.Response) {
        Write-Host "Status Code: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
        Write-Host "Status Description: $($_.Exception.Response.StatusDescription)" -ForegroundColor Red
    }
}
