#!/usr/bin/env pwsh
<#
.SYNOPSIS
    Simple Discord bot test
.DESCRIPTION
    Tests Discord bot with a simple text message first
#>

$ErrorActionPreference = "Stop"

# Load environment variables
if (Test-Path ".env") {
    Get-Content ".env" | ForEach-Object {
        if ($_ -match "^([^#][^=]+)=(.*)$") {
            $name = $matches[1].Trim()
            $value = $matches[2].Trim()
            [Environment]::SetEnvironmentVariable($name, $value, "Process")
        }
    }
}

$botToken = $env:DISCORD_BOT_TOKEN
$channelId = $env:DISCORD_CHANNEL_ID

Write-Host "Testing Discord Bot..." -ForegroundColor Green
Write-Host "Channel ID: $channelId" -ForegroundColor Cyan
Write-Host "Bot Token Length: $($botToken.Length)" -ForegroundColor Cyan

# Simple text message first
$payload = @{
    content = "🧪 Test message from SmaTrendFollower Bot"
} | ConvertTo-Json

$headers = @{
    "Authorization" = "Bot $botToken"
    "Content-Type" = "application/json"
    "User-Agent" = "SmaTrendFollower/1.0"
}

$apiUrl = "https://discord.com/api/v10/channels/$channelId/messages"

try {
    Write-Host "Sending simple test message..." -ForegroundColor Cyan
    Write-Host "URL: $apiUrl" -ForegroundColor Gray
    
    $response = Invoke-WebRequest -Uri $apiUrl -Method Post -Headers $headers -Body $payload -UseBasicParsing
    
    Write-Host "SUCCESS: Message sent!" -ForegroundColor Green
    Write-Host "Status Code: $($response.StatusCode)" -ForegroundColor Gray
    Write-Host "Response: $($response.Content)" -ForegroundColor Gray
    
} catch {
    Write-Host "ERROR: $($_.Exception.Message)" -ForegroundColor Red
    
    if ($_.Exception.Response) {
        $statusCode = $_.Exception.Response.StatusCode.value__
        Write-Host "HTTP Status Code: $statusCode" -ForegroundColor Red
        
        # Try to read the response body
        try {
            $stream = $_.Exception.Response.GetResponseStream()
            $reader = New-Object System.IO.StreamReader($stream)
            $responseBody = $reader.ReadToEnd()
            Write-Host "Response Body: $responseBody" -ForegroundColor Red
        } catch {
            Write-Host "Could not read response body" -ForegroundColor Yellow
        }
        
        # Common Discord API error codes
        switch ($statusCode) {
            401 { Write-Host "HINT: Invalid bot token" -ForegroundColor Yellow }
            403 { Write-Host "HINT: Bot lacks permission to send messages in this channel" -ForegroundColor Yellow }
            404 { Write-Host "HINT: Channel not found or bot not in server" -ForegroundColor Yellow }
            429 { Write-Host "HINT: Rate limited" -ForegroundColor Yellow }
            default { Write-Host "HINT: Unknown error code $statusCode" -ForegroundColor Yellow }
        }
    }
}
